import docx
import ollama

# === CONFIG ===
DOCX_PATH = r'E:\d_cp\swtichover.docx
MODEL_NAME = 'llama3.2'


# === Load and clean DOCX paragraphs ===
def load_docx_blocks(path):
    doc = docx.Document(path)
    blocks = []
    current_block = ""

    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:
            continue

        # Group lines into logical blocks (ends when there's an empty line or separator)
        if text.lower().startswith("output") or text.endswith(";"):
            current_block += text + "\n"
            blocks.append(current_block.strip())
            current_block = ""
        else:
            current_block += text + "\n"

    # Handle any remaining block
    if current_block.strip():
        blocks.append(current_block.strip())

    return blocks


# === Run inference with LLaMA 3.2 ===
def extract_info_from_block(text_block):
    prompt = f"""
You are an expert at extracting SOP steps.

For the following text, extract:
- Statement: What the user is trying to do.
- Command: Any command/query/script used.
- Output: Any response/result shown (if available).

Text:
{text_block}

Return only the extracted fields in this format:
Statement: ...
Command: ...
Output: ...
"""

    response = ollama.chat(model=MODEL_NAME, messages=[{"role": "user", "content": prompt}])
    return response['message']['content']


# === Main ===
if __name__ == "__main__":
    blocks = load_docx_blocks(DOCX_PATH)
    print(f"Found {len(blocks)} text blocks in the SOP.\n")

    for i, block in enumerate(blocks, 1):
        print(f"🧩 Block {i}:\n{block}\n")
        extracted = extract_info_from_block(block)
        print(f"✅ Extracted:\n{extracted}\n{'-' * 50}\n")
